// Session Management Utility
// Handles session ID generation and consistency for CSRF token workflow

class SessionManager {
  constructor() {
    this.sessionIdKey = 'sessionId';
    this.csrfSessionIdKey = 'csrfSessionId'; // Track session ID used for CSRF token
  }

  /**
   * Generate a new session ID using crypto.randomUUID or fallback
   */
  generateSessionId() {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return crypto.randomUUID();
    }
    
    // Fallback for older browsers
    return 'session-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Get or create a session ID for the current session
   */
  getOrCreateSessionId() {
    let sessionId = localStorage.getItem(this.sessionIdKey);
    
    if (!sessionId) {
      sessionId = this.generateSessionId();
      localStorage.setItem(this.sessionIdKey, sessionId);
      console.log('Generated new session ID:', sessionId);
    }
    
    return sessionId;
  }

  /**
   * Get the current session ID (without creating a new one)
   */
  getCurrentSessionId() {
    return localStorage.getItem(this.sessionIdKey);
  }

  /**
   * Set a specific session ID (useful when backend provides one)
   */
  setSessionId(sessionId) {
    if (sessionId) {
      localStorage.setItem(this.sessionIdKey, sessionId);
      console.log('Set session ID:', sessionId);
    }
  }

  /**
   * Clear the current session ID
   */
  clearSessionId() {
    localStorage.removeItem(this.sessionIdKey);
    localStorage.removeItem(this.csrfSessionIdKey);
    console.log('Cleared session ID');
  }

  /**
   * Get the session ID that was used for CSRF token generation
   */
  getCSRFSessionId() {
    return localStorage.getItem(this.csrfSessionIdKey);
  }

  /**
   * Set the session ID that was used for CSRF token generation
   */
  setCSRFSessionId(sessionId) {
    if (sessionId) {
      localStorage.setItem(this.csrfSessionIdKey, sessionId);
    }
  }

  /**
   * Ensure session ID consistency between CSRF token and API calls
   * Returns the session ID that should be used for both
   */
  ensureConsistentSessionId() {
    const currentSessionId = this.getCurrentSessionId();
    const csrfSessionId = this.getCSRFSessionId();
    
    // If we have a CSRF session ID, use it for consistency
    if (csrfSessionId) {
      if (currentSessionId && currentSessionId !== csrfSessionId) {
        console.warn('Session ID mismatch detected. Using CSRF session ID for consistency.');
        this.setSessionId(csrfSessionId);
      }
      return csrfSessionId;
    }
    
    // Otherwise, get or create a session ID
    return this.getOrCreateSessionId();
  }

  /**
   * Validate that the current session ID matches the CSRF session ID
   */
  validateSessionConsistency() {
    const currentSessionId = this.getCurrentSessionId();
    const csrfSessionId = this.getCSRFSessionId();
    
    if (csrfSessionId && currentSessionId && currentSessionId !== csrfSessionId) {
      console.error('Session ID inconsistency detected:', {
        current: currentSessionId,
        csrf: csrfSessionId
      });
      return false;
    }
    
    return true;
  }

  /**
   * Reset session for new conversation
   */
  resetSession() {
    this.clearSessionId();
    return this.getOrCreateSessionId();
  }

  /**
   * Get debug information about current session state
   */
  getDebugInfo() {
    return {
      currentSessionId: this.getCurrentSessionId(),
      csrfSessionId: this.getCSRFSessionId(),
      isConsistent: this.validateSessionConsistency(),
      timestamp: new Date().toISOString()
    };
  }
}

// Create singleton instance
const sessionManager = new SessionManager();

// Expose to window for debugging in development
if (process.env.NODE_ENV === 'development') {
  window.sessionManager = sessionManager;
}

export default sessionManager;
