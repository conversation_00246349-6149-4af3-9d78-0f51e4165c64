import sessionManager from '../utils/sessionManager';

class StreamingService {
  constructor() {
    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5529/api';
  }

  /**
   * Get CSRF token if needed with session consistency
   */
  async ensureCSRFToken(forceRefresh = false) {
    const token = localStorage.getItem('authToken');
    if (!token) return null; // No auth token, no CSRF needed

    let csrfToken = localStorage.getItem('csrfToken');

    // Check if we need to refresh the token
    if (!csrfToken || forceRefresh) {
      // Force refresh
      return await this.fetchNewCSRFToken(token);
    }

    // Validate session consistency with existing token
    if (!sessionManager.validateSessionConsistency()) {
      console.warn('Session inconsistency detected in streaming service, refreshing CSRF token');
      return await this.fetchNewCSRFToken(token);
    }

    return csrfToken;
  }

  /**
   * Fetch a new CSRF token with proper session handling
   */
  async fetchNewCSRFToken(authToken) {
    try {
      console.log('Fetching new CSRF token...');

      // Ensure session consistency
      const sessionId = sessionManager.ensureConsistentSessionId();

      const headers = {
        'Authorization': `Bearer ${authToken}`,
        'X-Session-ID': sessionId
      };

      const response = await fetch(`${this.baseURL}/auth/csrf-token`, {
        headers
      });

      const result = await response.json();
      if (result.success) {
        const { csrfToken, sessionId: returnedSessionId } = result.data;
        localStorage.setItem('csrfToken', csrfToken);

        // Update session tracking
        if (returnedSessionId) {
          sessionManager.setCSRFSessionId(returnedSessionId);
          if (returnedSessionId !== sessionId) {
            console.log('Backend returned different session ID in streaming service:', returnedSessionId);
            sessionManager.setSessionId(returnedSessionId);
          }
        } else {
          sessionManager.setCSRFSessionId(sessionId);
        }

        console.log('New CSRF token obtained with session ID:', returnedSessionId || sessionId);
        return csrfToken;
      } else {
        console.error('Failed to get CSRF token:', result);
      }
    } catch (error) {
      console.warn('Failed to fetch CSRF token:', error);
    }
    return null;
  }

  /**
   * Create streaming request with proper headers and session consistency
   */
  async createStreamingRequest(endpoint, data, options = {}) {
    const token = localStorage.getItem('authToken');

    const headers = {
      'Content-Type': 'application/json',
      ...options.headers
    };

    // Add authentication headers
    if (token) {
      headers.Authorization = `Bearer ${token}`;

      // Ensure we have a CSRF token with session consistency
      const csrfToken = await this.ensureCSRFToken();
      if (csrfToken) {
        headers['X-CSRF-Token'] = csrfToken;

        // Add the session ID that was used for CSRF token generation
        const csrfSessionId = sessionManager.getCSRFSessionId();
        if (csrfSessionId) {
          headers['X-Session-ID'] = csrfSessionId;
        }
      }
    } else {
      // For guest users, ensure we have a session ID
      const sessionId = sessionManager.getOrCreateSessionId();
      headers['X-Session-ID'] = sessionId;
    }

    // Include session ID in request body for backend processing
    const requestData = { ...data };
    const sessionIdForBody = sessionManager.getCurrentSessionId();
    if (sessionIdForBody) {
      requestData.sessionId = sessionIdForBody;
    }

    const requestOptions = {
      method: 'POST',
      headers,
      body: JSON.stringify(requestData),
      ...options
    };

    console.log('Creating streaming request:', {
      endpoint,
      headers: {
        'X-Session-ID': headers['X-Session-ID'],
        'X-CSRF-Token': headers['X-CSRF-Token'] ? `${headers['X-CSRF-Token'].substring(0, 10)}...` : 'none'
      },
      bodySessionId: requestData.sessionId,
      sessionManagerState: sessionManager.getDebugInfo()
    });

    return fetch(`${this.baseURL}${endpoint}`, requestOptions);
  }

  /**
   * Parse Server-Sent Events stream
   */
  async parseSSEStream(response, callbacks = {}) {
    const {
      onStart = () => {},
      onChunk = () => {},
      onComplete = () => {},
      onError = () => {},
      onProgress = () => {},
      onMetadata = () => {}
    } = callbacks;

    if (!response.ok) {
      const errorText = await response.text();

      // Handle CSRF token errors specifically
      if (response.status === 403 && (errorText.includes('CSRF') || errorText.includes('Invalid CSRF token'))) {
        // Clear invalid CSRF token and retry once
        localStorage.removeItem('csrfToken');
        throw new Error('CSRF token invalid. Please try again.');
      }

      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';
    let fullResponse = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6));
              
              switch (data.type) {
                case 'start':
                  onStart(data);
                  if (data.metadata) {
                    onMetadata(data.metadata);
                  }
                  break;

                case 'chunk':
                  fullResponse += data.content;
                  onChunk(data.content, fullResponse);
                  onProgress(fullResponse);
                  break;

                case 'complete':
                  onComplete(data.fullResponse || fullResponse, data);
                  return {
                    response: data.fullResponse || fullResponse,
                    metadata: data.metadata || {}
                  };

                case 'error':
                  onError(new Error(data.error));
                  throw new Error(data.error);

                default:
                  console.warn('Unknown SSE event type:', data.type);
              }
            } catch (parseError) {
              console.error('Failed to parse SSE data:', parseError, 'Line:', line);
            }
          }
        }
      }
    } catch (error) {
      onError(error);
      throw error;
    } finally {
      reader.releaseLock();
    }

    return {
      response: fullResponse,
      metadata: {}
    };
  }

  /**
   * Stream chat message (regular or guest)
   * Uses sessionId for conversation continuity
   */
  async streamChatMessage(message, options = {}) {
    const { sessionId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const data = {
      message,
      llmModel
    };

    // Set the session ID in session manager if provided from options
    // This ensures consistency between the provided session ID and session manager
    if (sessionId) {
      sessionManager.setSessionId(sessionId);
      console.log('Using provided session ID for chat message:', sessionId);
    }

    // Note: sessionId will be added by createStreamingRequest from session manager
    // This ensures consistency between headers and body

    try {
      const response = await this.createStreamingRequest('/chat/message/stream', data);
      return this.parseSSEStream(response, streamOptions);
    } catch (error) {
      // Retry once if CSRF token error with fresh token
      if (error.message.includes('CSRF token invalid')) {
        console.log('Retrying with fresh CSRF token...');
        // Force refresh CSRF token and retry
        await this.ensureCSRFToken(true);
        const retryResponse = await this.createStreamingRequest('/chat/message/stream', data);
        return this.parseSSEStream(retryResponse, streamOptions);
      }
      throw error;
    }
  }

  /**
   * Stream thread message
   * Uses threadId for conversation continuity
   */
  async streamThreadMessage(message, options = {}) {
    const { threadId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const data = {
      message,
      llmModel
    };

    // Include threadId for conversation continuity
    if (threadId) {
      data.threadId = threadId;
    }

    try {
      const response = await this.createStreamingRequest('/threads/message/stream', data);
      return this.parseSSEStream(response, streamOptions);
    } catch (error) {
      // Retry once if CSRF token error with fresh token
      if (error.message.includes('CSRF token invalid')) {
        console.log('Retrying with fresh CSRF token...');
        // Force refresh CSRF token and retry
        await this.ensureCSRFToken(true);
        const retryResponse = await this.createStreamingRequest('/threads/message/stream', data);
        return this.parseSSEStream(retryResponse, streamOptions);
      }
      throw error;
    }
  }

  /**
   * Stream project message
   * Uses threadId for conversation continuity within the project
   */
  async streamProjectMessage(projectId, message, options = {}) {
    const { threadId, llmModel = 'gpt-3.5-turbo', ...streamOptions } = options;

    const data = {
      message,
      llmModel
    };

    // Include threadId for conversation continuity within the project
    if (threadId) {
      data.threadId = threadId;
    }

    try {
      const response = await this.createStreamingRequest(`/projects/${projectId}/message/stream`, data);
      return this.parseSSEStream(response, streamOptions);
    } catch (error) {
      // Retry once if CSRF token error with fresh token
      if (error.message.includes('CSRF token invalid')) {
        console.log('Retrying with fresh CSRF token...');
        // Force refresh CSRF token and retry
        await this.ensureCSRFToken(true);
        const retryResponse = await this.createStreamingRequest(`/projects/${projectId}/message/stream`, data);
        return this.parseSSEStream(retryResponse, streamOptions);
      }
      throw error;
    }
  }

  /**
   * Handle streaming errors
   */
  handleStreamingError(error) {
    if (error.name === 'AbortError') {
      return new Error('Request was cancelled');
    }
    
    if (error.message.includes('Failed to fetch')) {
      return new Error('Network error: Please check your connection');
    }
    
    if (error.message.includes('HTTP 401')) {
      return new Error('Authentication required');
    }
    
    if (error.message.includes('HTTP 403')) {
      return new Error('Access denied');
    }
    
    if (error.message.includes('HTTP 429')) {
      return new Error('Rate limit exceeded. Please wait before sending another message.');
    }
    
    return error;
  }
}

const streamingService = new StreamingService();
export default streamingService;
