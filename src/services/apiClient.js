import axios from 'axios';
import apiCallTracker from '../utils/apiCallTracker';
import apiPayloadDebugger from '../utils/apiPayloadDebugger';
import sessionManager from '../utils/sessionManager';

// Create axios instance with base configuration
const apiClient = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5529/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Track CSRF token fetching to prevent duplicate requests
let csrfTokenPromise = null;

// Request interceptor to add auth token and CSRF token
apiClient.interceptors.request.use(
  async (config) => {
    // Track API call for debugging duplicates
    apiCallTracker.track(config.url, config.method, config.data);

    // Debug API payload for common issues
    if (config.data) {
      apiPayloadDebugger.interceptRequest(config.url, config.method, config.data);
    }

    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF token for all authenticated requests (when auth token is present)
    if (token) {
      let csrfToken = localStorage.getItem('csrfToken');

      if (!csrfToken) {
        // Prevent multiple simultaneous CSRF token requests
        if (!csrfTokenPromise) {
          csrfTokenPromise = (async () => {
            try {
              // Ensure session consistency before requesting CSRF token
              const sessionId = sessionManager.ensureConsistentSessionId();

              const headers = {
                'X-Session-ID': sessionId,
                'Authorization': `Bearer ${token}`
              };

              const csrfResponse = await axios.get(`${config.baseURL}/auth/csrf-token`, {
                headers
              });

              if (csrfResponse.data.success) {
                const { csrfToken: newCsrfToken, sessionId: returnedSessionId } = csrfResponse.data.data;
                localStorage.setItem('csrfToken', newCsrfToken);

                // Store the session ID used for CSRF token
                if (returnedSessionId) {
                  sessionManager.setCSRFSessionId(returnedSessionId);
                  if (returnedSessionId !== sessionId) {
                    sessionManager.setSessionId(returnedSessionId);
                  }
                } else {
                  sessionManager.setCSRFSessionId(sessionId);
                }

                return newCsrfToken;
              }
              return null;
            } catch (csrfError) {
              console.warn('Failed to get CSRF token:', csrfError);
              return null;
            } finally {
              csrfTokenPromise = null;
            }
          })();
        }

        csrfToken = await csrfTokenPromise;
      } else {
        // Validate session consistency with existing token
        if (!sessionManager.validateSessionConsistency()) {
          console.warn('Session inconsistency detected in API client, clearing CSRF token');
          localStorage.removeItem('csrfToken');
          csrfToken = null;

          // Retry getting CSRF token with consistent session
          if (!csrfTokenPromise) {
            csrfTokenPromise = (async () => {
              try {
                const sessionId = sessionManager.ensureConsistentSessionId();
                const headers = {
                  'X-Session-ID': sessionId,
                  'Authorization': `Bearer ${token}`
                };

                const csrfResponse = await axios.get(`${config.baseURL}/auth/csrf-token`, {
                  headers
                });

                if (csrfResponse.data.success) {
                  const { csrfToken: newCsrfToken, sessionId: returnedSessionId } = csrfResponse.data.data;
                  localStorage.setItem('csrfToken', newCsrfToken);

                  if (returnedSessionId) {
                    sessionManager.setCSRFSessionId(returnedSessionId);
                    if (returnedSessionId !== sessionId) {
                      sessionManager.setSessionId(returnedSessionId);
                    }
                  } else {
                    sessionManager.setCSRFSessionId(sessionId);
                  }

                  return newCsrfToken;
                }
                return null;
              } catch (csrfError) {
                console.warn('Failed to refresh CSRF token:', csrfError);
                return null;
              } finally {
                csrfTokenPromise = null;
              }
            })();
          }

          csrfToken = await csrfTokenPromise;
        }
      }

      if (csrfToken) {
        config.headers['X-CSRF-Token'] = csrfToken;

        // Add session ID to ensure consistency
        const sessionId = sessionManager.getCSRFSessionId() || sessionManager.getCurrentSessionId();
        if (sessionId) {
          config.headers['X-Session-ID'] = sessionId;
        }
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('user');
      localStorage.removeItem('csrfToken');
      window.location.href = '/signin';
    } else if (error.response?.status === 403 &&
               error.response?.data?.message?.includes('CSRF') &&
               !originalRequest._retry) {
      // CSRF token invalid, clear it and retry once
      originalRequest._retry = true;
      localStorage.removeItem('csrfToken');
      csrfTokenPromise = null; // Reset the promise so it can be fetched again

      try {
        // The request interceptor will automatically fetch a new CSRF token
        return await apiClient(originalRequest);
      } catch (retryError) {
        return Promise.reject(retryError);
      }
    }

    return Promise.reject(error);
  }
);

// Utility function to manually refresh CSRF token with session consistency
export const refreshCSRFToken = async () => {
  localStorage.removeItem('csrfToken');
  csrfTokenPromise = null;

  try {
    const baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5529/api';

    // Ensure session consistency
    const sessionId = sessionManager.ensureConsistentSessionId();

    const headers = {
      'X-Session-ID': sessionId
    };

    // Include auth token if available
    const authToken = localStorage.getItem('authToken');
    if (authToken) {
      headers.Authorization = `Bearer ${authToken}`;
    }

    console.log('Refreshing CSRF token with session ID:', sessionId);

    const csrfResponse = await axios.get(`${baseURL}/auth/csrf-token`, {
      headers
    });

    if (csrfResponse.data.success) {
      const { csrfToken: newCsrfToken, sessionId: returnedSessionId } = csrfResponse.data.data;
      localStorage.setItem('csrfToken', newCsrfToken);

      // Update session tracking
      if (returnedSessionId) {
        sessionManager.setCSRFSessionId(returnedSessionId);
        if (returnedSessionId !== sessionId) {
          console.log('Backend returned different session ID during refresh:', returnedSessionId);
          sessionManager.setSessionId(returnedSessionId);
        }
      } else {
        sessionManager.setCSRFSessionId(sessionId);
      }

      console.log('CSRF token refreshed successfully with session ID:', returnedSessionId || sessionId);
      return newCsrfToken;
    }
    throw new Error('Failed to refresh CSRF token');
  } catch (error) {
    console.error('Error refreshing CSRF token:', error);
    throw error;
  }
};

export default apiClient;
