# CSRF Token Issue Fix Documentation

## Problem Description

The `/api/chat/message/stream` endpoint was returning "Invalid CSRF token" errors due to **session ID mismatch** between CSRF token generation and validation.

## Root Cause Analysis

### The Issue
The CSRF protection system uses session IDs to bind tokens to specific sessions. However, there was an inconsistency in how session IDs were resolved between:

1. **Token Generation** (`GET /api/auth/csrf-token`)
2. **Token Validation** (`POST /api/chat/message/stream`)

### Session ID Resolution Priority (Before Fix)

**Token Generation:**
```javascript
const sessionId = 
  req.headers['x-session-id'] ||
  req.query.sessionId ||
  'default';
```

**Token Validation:**
```javascript
const sessionId = 
  req.headers['x-session-id'] ||
  req.query.sessionId ||
  req.body.sessionId ||
  (req as any).sessionId ||  // Set by extractSessionId middleware
  'default';
```

### The Problem
- The `extractSessionId` middleware runs before CSRF protection
- It extracts session ID and sets `(req as any).sessionId`
- But the CSRF validation logic didn't prioritize this extracted session ID
- This caused mismatches when session IDs were provided in different ways

## Solution Implemented

### 1. Consistent Session ID Resolution
Created a centralized `getSessionId()` method with consistent priority order:

```javascript
private static getSessionId(req: Request): string {
  // Priority order for session ID resolution:
  // 1. Extracted session ID (set by extractSessionId middleware)
  // 2. Header X-Session-ID
  // 3. Query parameter sessionId
  // 4. Body sessionId
  // 5. Default fallback
  return (req as any).sessionId ||
         req.headers['x-session-id'] as string ||
         req.query.sessionId as string ||
         req.body.sessionId as string ||
         'default';
}
```

### 2. Updated CSRF Protection Logic
- Both token generation and validation now use the same `getSessionId()` method
- Added debug logging for better troubleshooting
- Enhanced error messages with partial token information

### 3. Added extractSessionId Middleware to CSRF Token Route
```javascript
// CSRF token endpoint
router.get('/csrf-token',
  extractSessionId,  // Added this middleware
  CSRFProtection.getToken()
);
```

## Files Modified

1. **`src/middleware/security.ts`**
   - Added `getSessionId()` private method
   - Updated `protect()` method to use consistent session ID resolution
   - Updated `getToken()` method to use same logic
   - Added debug logging and enhanced error messages

2. **`src/routes/auth.ts`**
   - Added `extractSessionId` import
   - Added `extractSessionId` middleware to CSRF token endpoint

## Testing Results

### ✅ Working Scenarios
1. **Session ID in header**: `X-Session-ID: session-123`
2. **Session ID in query**: `?sessionId=session-123`
3. **Session ID in body**: `{"sessionId": "session-123"}`
4. **Mixed scenarios**: Token generated with header, used with body session ID

### ✅ Security Validation
1. **Mismatched session IDs**: Correctly rejected with "Invalid CSRF token"
2. **Missing CSRF token**: Correctly rejected with "CSRF token required"
3. **Expired tokens**: Correctly rejected (1-hour expiry)

## Usage Examples

### Correct Workflow

1. **Generate CSRF Token**:
```bash
curl -X GET "http://localhost:5529/api/auth/csrf-token" \
  -H "X-Session-ID: my-session-123"
```

Response:
```json
{
  "success": true,
  "message": "CSRF token generated",
  "data": {
    "csrfToken": "abc123...",
    "sessionId": "my-session-123"
  }
}
```

2. **Use Token in API Call**:
```bash
curl -X POST "http://localhost:5529/api/chat/message/stream" \
  -H "Content-Type: application/json" \
  -H "X-Session-ID: my-session-123" \
  -H "X-CSRF-Token: abc123..." \
  -d '{"message": "Hello", "sessionId": "my-session-123"}'
```

### Alternative: Session ID in Body
```bash
# Generate token
curl -X GET "http://localhost:5529/api/auth/csrf-token?sessionId=my-session-123"

# Use token
curl -X POST "http://localhost:5529/api/chat/message/stream" \
  -H "Content-Type: application/json" \
  -H "X-CSRF-Token: abc123..." \
  -d '{"message": "Hello", "sessionId": "my-session-123"}'
```

## Key Takeaways

1. **Session ID Consistency**: Always use the same session ID for token generation and API calls
2. **Multiple Sources Supported**: Session ID can be provided via header, query, or body
3. **Middleware Order Matters**: `extractSessionId` must run before `CSRFProtection.protect()`
4. **Debug Information**: CSRF token responses now include the session ID for verification

## Prevention

To prevent similar issues in the future:

1. **Centralize session ID resolution logic**
2. **Use consistent middleware ordering**
3. **Add comprehensive logging for debugging**
4. **Include session ID in token generation responses**
5. **Write integration tests for CSRF workflows**
